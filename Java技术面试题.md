# Java技术面试题 - 基于简历分析

## 第一部分：Java基础知识考查

### 1. 集合框架相关
**问题1：** 你在项目中使用过哪些集合类？HashMap和ConcurrentHashMap的区别是什么？在什么场景下会选择使用ConcurrentHashMap？

**参考答案：**
- HashMap：非线程安全，适用于单线程环境，底层基于数组+链表+红黑树实现
- ConcurrentHashMap：线程安全，适用于多线程环境，JDK1.8后采用CAS+synchronized实现
- 使用场景：在多线程环境下需要共享Map数据时使用ConcurrentHashMap，如缓存场景

**问题2：** 你提到熟悉反射，能说说反射的应用场景吗？在Spring框架中反射是如何被使用的？

**参考答案：**
- 反射应用场景：动态创建对象、调用方法、获取类信息、框架开发
- Spring中的应用：IOC容器通过反射创建Bean实例、AOP代理对象创建、注解处理等

### 2. 多线程与并发
**问题3：** 你在"天津法院结案文书统计任务管理平台"中使用了线程池，能详细说说你是如何配置线程池的？核心参数都设置了什么值？

**参考答案：**
```java
@Configuration
public class ThreadPoolConfig {
    @Bean
    public ThreadPoolExecutor taskExecutor() {
        return new ThreadPoolExecutor(
            10, // corePoolSize：核心线程数
            20, // maximumPoolSize：最大线程数  
            60L, // keepAliveTime：空闲线程存活时间
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100), // workQueue：工作队列
            new ThreadFactoryBuilder().setNameFormat("task-pool-%d").build(),
            new CustomRejectedExecutionHandler() // 自定义拒绝策略
        );
    }
}
```

**问题4：** 你提到自定义了线程池的拒绝策略，能说说都有哪些拒绝策略？你的自定义策略是如何实现的？

**参考答案：**
- 四种拒绝策略：AbortPolicy(抛异常)、CallerRunsPolicy(调用者执行)、DiscardPolicy(丢弃)、DiscardOldestPolicy(丢弃最老的)
- 自定义策略可能是将任务发送到MQ进行持久化，避免任务丢失

## 第二部分：Spring框架深入

### 5. Spring Boot与IOC/AOP
**问题5：** 你熟悉Spring的IOC和AOP，能说说Spring Bean的生命周期吗？

**参考答案：**
1. 实例化Bean
2. 设置Bean属性
3. 调用BeanNameAware的setBeanName()
4. 调用BeanFactoryAware的setBeanFactory()
5. 调用ApplicationContextAware的setApplicationContext()
6. 调用BeanPostProcessor的预初始化方法
7. 调用InitializingBean的afterPropertiesSet()
8. 调用自定义init方法
9. 调用BeanPostProcessor的后初始化方法
10. Bean可以使用了
11. 容器关闭时调用DisposableBean的destroy()
12. 调用自定义destroy方法

**问题6：** 在你的项目中，AOP主要用在什么地方？能手写一个简单的AOP切面吗？

**参考答案：**
```java
@Aspect
@Component
public class LogAspect {
    
    @Pointcut("@annotation(com.example.annotation.Log)")
    public void logPointcut() {}
    
    @Around("logPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            log.info("方法执行时间：{}ms", endTime - startTime);
            return result;
        } catch (Exception e) {
            log.error("方法执行异常：", e);
            throw e;
        }
    }
}
```

## 第三部分：数据库与缓存

### 7. MySQL优化相关
**问题7：** 你提到熟悉SQL性能调优，在你的项目中遇到过哪些性能问题？是如何解决的？

**参考答案：**
- 问题：案件查询模块查询慢
- 解决方案：
  1. 使用EXPLAIN分析执行计划
  2. 添加合适的索引
  3. 优化SQL语句，避免全表扫描
  4. 分页查询优化

**问题8：** 你在"诉前送达平台"中提到通过索引解决了检索速度慢的问题，能具体说说是什么类型的索引？索引的设计原则是什么？

**参考答案：**
- 索引类型：可能是复合索引、覆盖索引
- 设计原则：
  1. 最左前缀原则
  2. 选择性高的字段优先
  3. 避免过多索引影响写性能
  4. 考虑覆盖索引减少回表

### 8. Redis应用
**问题9：** 你在文书检索系统中使用Redis做缓存，能说说你是如何设计缓存策略的？如何解决缓存穿透和缓存雪崩问题？

**参考答案：**
- 缓存策略：Cache-Aside模式，先查缓存，缓存miss再查数据库
- 缓存穿透：布隆过滤器、缓存空值
- 缓存雪崩：设置随机过期时间、多级缓存、熔断降级

## 第四部分：消息队列与搜索引擎

### 10. RabbitMQ应用
**问题10：** 在任务管理平台中，你使用RabbitMQ进行任务持久化，能说说你的消息队列设计吗？如何保证消息不丢失？

**参考答案：**
```java
// 消息队列配置
@Configuration
public class RabbitConfig {
    
    @Bean
    public Queue taskQueue() {
        return QueueBuilder.durable("task.queue")
                .withArgument("x-dead-letter-exchange", "task.dlx")
                .build();
    }
    
    @Bean
    public DirectExchange taskExchange() {
        return new DirectExchange("task.exchange", true, false);
    }
}

// 消息发送
@Component
public class TaskProducer {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    public void sendTask(TaskMessage task) {
        rabbitTemplate.convertAndSend("task.exchange", "task.routing.key", 
            task, message -> {
                message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                return message;
            });
    }
}
```

保证消息不丢失：
1. 消息持久化
2. 生产者确认机制
3. 消费者手动ACK
4. 死信队列处理失败消息

### 11. Elasticsearch应用
**问题11：** 你在文书检索系统中使用了ES，能说说你是如何设计索引的？分词器是如何选择的？

**参考答案：**
```json
{
  "mappings": {
    "properties": {
      "title": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "content": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "court": {
        "type": "keyword"
      },
      "case_type": {
        "type": "keyword"
      },
      "judge_date": {
        "type": "date",
        "format": "yyyy-MM-dd"
      }
    }
  }
}
```

分词器选择：
- ik_max_word：索引时最大化分词，提高召回率
- ik_smart：搜索时智能分词，提高精确度

## 第五部分：项目深入与架构设计

### 12. 并发控制
**问题12：** 你提到在非诉解纷平台中使用synchronized解决案号生成的并发问题，还有其他解决方案吗？在分布式环境下如何处理？

**参考答案：**
单机环境解决方案：
1. synchronized关键字
2. ReentrantLock
3. 数据库唯一约束
4. 原子类AtomicLong

分布式环境解决方案：
1. Redis分布式锁
2. Zookeeper分布式锁
3. 数据库悲观锁
4. 分布式ID生成器（雪花算法）

### 13. 动态数据源
**问题13：** 你使用了Dynamic Datasource，能说说多数据源的切换原理吗？在事务管理上需要注意什么？

**参考答案：**
切换原理：
1. 基于ThreadLocal存储数据源标识
2. 继承AbstractRoutingDataSource重写determineCurrentLookupKey()
3. 通过AOP在方法执行前切换数据源

事务管理注意事项：
1. 不同数据源的事务是独立的
2. 跨数据源操作需要分布式事务
3. 可以使用@Transactional的propagation控制事务传播

### 14. 系统优化
**问题14：** 在你负责的系统中，如果并发量突然增加10倍，你会从哪些方面进行优化？

**参考答案：**
1. **应用层优化**：
   - 增加服务器实例，负载均衡
   - 优化代码逻辑，减少不必要的计算
   - 使用异步处理，提高吞吐量

2. **缓存优化**：
   - 增加Redis集群
   - 使用本地缓存减少网络开销
   - 优化缓存策略和过期时间

3. **数据库优化**：
   - 读写分离
   - 分库分表
   - 连接池优化

4. **消息队列**：
   - 增加消费者数量
   - 队列分片
   - 批量处理消息

### 15. 设计模式应用
**问题15：** 你提到熟悉多种设计模式，能结合你的项目经历说说在哪里使用了这些模式吗？

**参考答案：**
1. **工厂模式**：在案件分案系统中，根据不同的分案策略创建不同的处理器
2. **策略模式**：结合EasyRule规则引擎，不同的分案策略对应不同的策略实现
3. **观察者模式**：WebSocket实时推送任务进度，任务状态变化时通知前端
4. **外观模式**：封装复杂的ES查询操作，提供简单的搜索接口
5. **状态模式**：任务管理中，任务的不同状态对应不同的处理逻辑

## 第六部分：开放性问题

### 16. 技术选型
**问题16：** 如果让你重新设计文书检索系统，你会如何进行技术选型？为什么？

### 17. 问题排查
**问题17：** 在生产环境中，如果用户反馈系统响应很慢，你会如何排查问题？

### 18. 未来规划
**问题18：** 你觉得自己在技术上还有哪些需要提升的地方？有什么学习计划？

---

## 面试评分标准

### 基础知识（30分）
- Java基础概念理解准确
- 集合、多线程、IO等核心知识掌握扎实

### 框架应用（25分）
- Spring Boot、MyBatis等框架使用熟练
- 能够解释框架原理和最佳实践

### 项目经验（25分）
- 能够清晰描述项目架构和技术难点
- 解决方案合理，有实际操作经验

### 问题解决能力（20分）
- 能够分析问题根因
- 提出合理的优化方案
- 具备系统性思维

**总分：100分**
**及格线：70分**
