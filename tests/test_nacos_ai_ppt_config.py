#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：初始化NacosConfigManager并获取ai_ppt配置
验证改进版本的Nacos配置管理器功能
获取 DataId: wuying-alpha-service:ai_ppt / Group: DEFAULT_GROUP 的值
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def test_nacos_ai_ppt_config():
    """测试获取ai_ppt配置"""
    print("=" * 80)
    print("🚀 测试NacosConfigManager获取ai_ppt配置")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        from src.shared.config.nacos_config import NacosConfigManager
        from shared.config.environments import env_manager

        # 显示当前环境信息
        env_info = env_manager.get_environment_info()
        print(f"\n🌍 环境信息:")
        print(f"  - 当前环境: {env_info['current_environment']}")
        print(f"  - 是否生产环境: {env_info['is_production']}")
        print(f"  - 是否开发环境: {env_info['is_development']}")
        
        # 1. 初始化NacosConfigManager
        print(f"\n📋 步骤1: 初始化NacosConfigManager")
        nacos_manager = NacosConfigManager()
        print("✅ NacosConfigManager初始化完成")
        
        # 2. 获取连接信息
        print(f"\n📋 步骤2: 检查连接信息")
        # 2.5. 健康状态检查
        # 3. 指定目标配置参数
        target_data_id = "wuying-alpha-service:ppt"
        target_group = "DEFAULT_GROUP"
        
        print(f"\n📋 步骤3: 目标配置参数")
        print(f"  - DataId: {target_data_id}")
        print(f"  - Group: {target_group}")
        
        # 4. 获取配置
        print(f"\n📋 步骤4: 获取配置内容")
        print("🔍 正在从Nacos获取配置...")

        config = nacos_manager.get_config(
            data_id=target_data_id,
            group=target_group
        )
        
        if config:
            print(f"✅ 成功获取配置，共 {len(config)} 个配置项")
            
            # 5. 显示配置内容
            print(f"\n📋 步骤5: 配置内容详情")
            print("=" * 60)
            
            if isinstance(config, dict):
                # 格式化输出JSON配置
                formatted_config = json.dumps(config, indent=2, ensure_ascii=False)
                print(formatted_config)
                
                # 显示配置结构概览
                print("\n🔧 配置结构概览:")
                for key, value in config.items():
                    if isinstance(value, dict):
                        print(f"  📁 {key}: 对象 ({len(value)} 个属性)")
                        # 显示前3个子属性
                        sub_keys = list(value.keys())[:3]
                        for sub_key in sub_keys:
                            sub_value = value[sub_key]
                            if isinstance(sub_value, (str, int, float, bool)):
                                print(f"    └─ {sub_key}: {sub_value}")
                            else:
                                print(f"    └─ {sub_key}: {type(sub_value).__name__}")
                        if len(value) > 3:
                            print(f"    └─ ... 还有 {len(value) - 3} 个属性")
                    elif isinstance(value, list):
                        print(f"  📋 {key}: 数组 ({len(value)} 个元素)")
                        if value:
                            print(f"    └─ 首个元素: {value[0]}")
                    else:
                        # 处理长字符串
                        str_value = str(value)
                        if len(str_value) > 100:
                            str_value = str_value[:100] + "..."
                        print(f"  📄 {key}: {str_value}")
            else:
                # 非字典类型的配置
                print("📄 原始配置内容:")
                print(config)
            
            print("=" * 60)
            
            # 6. 测试获取特定配置值
            print(f"\n📋 步骤6: 测试获取特定配置值")
            test_keys = [
                "app.name",
                "app.version", 
                "aippt.enabled",
                "aippt.endpoint",
                "aippt.access_key",
                "aippt.secret_key",
                "aippt.channel",
                "aippt.token_uid",
                "service.name",
                "features",
                "config.version"
            ]
            
            print("🔍 尝试获取以下配置值:")
            for key in test_keys:
                value = nacos_manager.get_config_value(
                    key=key,
                    default="[未找到]",
                    data_id=target_data_id,
                    group=target_group
                )
                
                # 处理敏感信息显示
                if any(sensitive in key.lower() for sensitive in ['key', 'password', 'secret', 'token']):
                    if value != "[未找到]" and len(str(value)) > 8:
                        display_value = str(value)[:4] + "****" + str(value)[-4:]
                    else:
                        display_value = value
                else:
                    display_value = value
                
                status = "✅" if value != "[未找到]" else "❌"
                print(f"  {status} {key}: {display_value}")
            
            # 7. 配置有效性检查
            print(f"\n📋 步骤7: 配置有效性检查")
            
            # 检查必要的aippt配置项
            required_aippt_keys = [
                "aippt.endpoint",
                "aippt.access_key", 
                "aippt.secret_key",
                "aippt.channel"
            ]
            
            missing_keys = []
            for key in required_aippt_keys:
                value = nacos_manager.get_config_value(
                    key=key,
                    default=None,
                    data_id=target_data_id,
                    group=target_group
                )
                if not value:
                    missing_keys.append(key)
            
            if missing_keys:
                print(f"⚠️ 缺少必要的配置项: {', '.join(missing_keys)}")
            else:
                print("✅ 所有必要的aippt配置项都存在")
            
            return config
            
        else:
            print("❌ 未获取到配置内容")
            print("\n🔍 可能的原因:")
            print("  1. 配置不存在于Nacos中")
            print("  2. Nacos服务不可用")
            print("  3. 网络连接问题")
            print("  4. DataId或Group参数错误")
            print("  5. 权限不足")
            
            # 尝试获取一个已知存在的配置以验证连接
            print(f"\n🔧 尝试获取其他配置以验证连接...")
            try:
                # 尝试获取一些可能存在的配置
                test_configs = [
                    ("wuying-alpha-service:application", "DEFAULT_GROUP"),
                    ("wuying-alpha-service:aippt_config", "DEFAULT_GROUP"),
                ]

                connection_ok = False
                for test_data_id, test_group in test_configs:
                    try:
                        test_config = nacos_manager.get_config(
                            data_id=test_data_id,
                            group=test_group
                        )
                        if test_config:
                            print(f"✅ 连接验证成功，找到配置: {test_group}:{test_data_id}")
                            connection_ok = True
                            break
                    except:
                        continue

                if not connection_ok:
                    print("❌ 连接验证失败，无法获取任何配置")

            except Exception as e:
                print(f"❌ 连接验证出错: {e}")
            
            return None
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已安装所需依赖:")
        print("  pip install nacos-sdk-python")
        print("  pip install dynaconf")
        return None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_direct_nacos_client():
    """使用原生Nacos客户端直接测试"""
    print(f"\n" + "=" * 80)
    print("🔧 使用原生Nacos客户端直接测试")
    print("=" * 80)

    try:
        import nacos
        from src.shared.config.environments import env_manager

        # 获取环境配置
        config = env_manager.get_config()
        endpoint = config.nacos_endpoint

        print(f"📡 Nacos端点: {endpoint}")

        # 解析endpoint，确保类型安全
        endpoint_str = str(endpoint) if endpoint else ""
        if "://" in endpoint_str:
            from urllib.parse import urlparse
            parsed = urlparse(endpoint_str)
            server_addresses = f"{parsed.hostname}:{parsed.port or 8848}"
        else:
            server_addresses = endpoint_str

        print(f"🔗 服务器地址: {server_addresses}")

        # 创建Nacos客户端
        client = nacos.NacosClient(
            server_addresses=server_addresses,
            namespace="",
            username="",
            password=""
        )

        # 获取目标配置
        target_data_id = "wuying-alpha-service:ai_ppt"
        target_group = "DEFAULT_GROUP"

        print(f"🎯 获取配置: {target_group}:{target_data_id}")

        config_content = client.get_config(
            data_id=target_data_id,
            group=target_group,
            timeout=10
        )

        if config_content:
            print(f"✅ 成功获取原始配置内容 ({len(config_content)} 字符)")
            print("\n📄 原始配置内容:")
            print("-" * 60)
            print(config_content)
            print("-" * 60)

            # 尝试解析配置
            try:
                parsed_config = json.loads(config_content)
                print("✅ 配置为有效JSON格式")
                return parsed_config
            except json.JSONDecodeError:
                try:
                    import yaml
                    parsed_config = yaml.safe_load(config_content)
                    print("✅ 配置为有效YAML格式")
                    return parsed_config
                except:
                    print("⚠️ 配置为纯文本格式")
                    return {"raw_content": config_content}
        else:
            print("❌ 未获取到配置内容")

            # 尝试一些可能的配置名称
            print("\n🔍 尝试获取其他可能的配置:")
            possible_configs = [
                ("wuying-alpha-service:aippt", "DEFAULT_GROUP"),
                ("wuying-alpha-service:aippt_config", "DEFAULT_GROUP"),
                ("wuying-alpha-service:application", "DEFAULT_GROUP"),
                ("alpha-service:ai_ppt", "DEFAULT_GROUP"),
                ("ai_ppt", "DEFAULT_GROUP"),
                ("aippt_config", "DEFAULT_GROUP"),
            ]

            found_configs = []
            for data_id, group in possible_configs:
                try:
                    test_config = client.get_config(
                        data_id=data_id,
                        group=group,
                        timeout=5
                    )
                    if test_config:
                        print(f"  ✅ 找到配置: {group}:{data_id} ({len(test_config)} 字符)")
                        found_configs.append((data_id, group, test_config))
                        # 显示配置内容预览
                        preview = test_config[:200] + "..." if len(test_config) > 200 else test_config
                        print(f"    预览: {preview}")
                    else:
                        print(f"  ❌ 未找到: {group}:{data_id}")
                except Exception as e:
                    print(f"  ❌ 错误: {group}:{data_id} - {e}")

            # 如果找到了配置，返回第一个
            if found_configs:
                data_id, group, content = found_configs[0]
                print(f"\n💡 使用找到的配置: {group}:{data_id}")
                try:
                    parsed_config = json.loads(content)
                    return parsed_config
                except:
                    try:
                        import yaml
                        parsed_config = yaml.safe_load(content)
                        return parsed_config
                    except:
                        return {"raw_content": content}

            return None

    except Exception as e:
        print(f"❌ 直接客户端测试失败: {e}")
        return None


def main():
    """主函数"""
    print("🚀 开始测试NacosConfigManager获取ai_ppt配置")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: 使用NacosConfigManager
    config1 = test_nacos_ai_ppt_config()
    
    # 测试2: 使用原生Nacos客户端
    config2 = test_direct_nacos_client()
    
    # 结果对比
    print(f"\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    if config1:
        print("✅ NacosConfigManager测试: 成功")
    else:
        print("❌ NacosConfigManager测试: 失败")
    
    if config2:
        print("✅ 原生Nacos客户端测试: 成功")
    else:
        print("❌ 原生Nacos客户端测试: 失败")
    
    if config1 or config2:
        print("\n🎉 至少有一种方式成功获取到配置")
        return True
    else:
        print("\n💔 两种方式都未能获取到配置")
        print("\n📋 建议检查:")
        print("  1. Nacos服务器是否正常运行")
        print("  2. 配置 'wuying-alpha-service:ai_ppt' 是否存在")
        print("  3. 网络连接是否正常")
        print("  4. 环境配置是否正确")
        return False


if __name__ == "__main__":
    success = main()
    print(f"\n{'='*80}")
    print(f"🏁 测试完成，结果: {'成功' if success else '失败'}")
    print(f"{'='*80}")
    sys.exit(0 if success else 1)
